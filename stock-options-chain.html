<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stock Options Chain Analyzer</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f2f5;
            padding: 20px;
        }
        .container {
            background-color: #ffffff;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 20px;
            max-width: 1400px;
            margin: 20px auto;
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px 12px;
            text-align: right;
            border: 1px solid #e5e7eb;
            font-size: 0.9rem;
        }
        th {
            background-color: #f3f4f6;
            position: sticky;
            top: 0;
        }
        .calls {
            background-color: rgba(34, 197, 94, 0.1);
        }
        .puts {
            background-color: rgba(239, 68, 68, 0.1);
        }
        .positive {
            color: #10b981;
        }
        .negative {
            color: #ef4444;
        }
        .center-column {
            background-color: #f8fafc;
            font-weight: bold;
        }
        .highlight {
            background-color: #fef3c7;
        }
        .max-pain {
            background-color: #ddd6fe;
            font-weight: bold;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }
        .stock-header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Stock Header Section -->
        <div class="stock-header">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold mb-2">
                        <select id="stockSelector" class="bg-transparent border-2 border-white rounded px-3 py-1 text-white">
                            <option value="INFY" class="text-black">INFY - Infosys</option>
                            <option value="TCS" class="text-black">TCS - Tata Consultancy</option>
                            <option value="RELIANCE" class="text-black">RELIANCE - Reliance Industries</option>
                            <option value="HDFCBANK" class="text-black">HDFCBANK - HDFC Bank</option>
                            <option value="ICICIBANK" class="text-black">ICICIBANK - ICICI Bank</option>
                            <option value="SBIN" class="text-black">SBIN - State Bank of India</option>
                            <option value="BHARTIARTL" class="text-black">BHARTIARTL - Bharti Airtel</option>
                            <option value="ITC" class="text-black">ITC - ITC Limited</option>
                            <option value="KOTAKBANK" class="text-black">KOTAKBANK - Kotak Mahindra Bank</option>
                            <option value="LT" class="text-black">LT - Larsen & Toubro</option>
                        </select>
                        Stock Options Chain
                    </h1>
                    <div class="flex space-x-6 text-lg">
                        <div>
                            <span class="opacity-80">Spot Price:</span>
                            <span id="stockSpotPrice" class="font-bold text-2xl ml-2">Loading...</span>
                            <span id="stockPriceChange" class="ml-2"></span>
                        </div>
                        <div>
                            <span class="opacity-80">Expiry:</span>
                            <select id="stockExpirySelector" class="bg-transparent border border-white rounded px-2 py-1 ml-2">
                                <option value="">Loading...</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <button id="stockRefreshButton" class="bg-white text-purple-600 px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        🔄 Refresh Data
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="stats-card">
                <h3 class="text-sm opacity-80">Max Pain Strike</h3>
                <p id="maxPainStrike" class="text-2xl font-bold">-</p>
            </div>
            <div class="stats-card">
                <h3 class="text-sm opacity-80">Total Call OI</h3>
                <p id="totalCallOI" class="text-2xl font-bold">-</p>
            </div>
            <div class="stats-card">
                <h3 class="text-sm opacity-80">Total Put OI</h3>
                <p id="totalPutOI" class="text-2xl font-bold">-</p>
            </div>
            <div class="stats-card">
                <h3 class="text-sm opacity-80">Put/Call Ratio</h3>
                <p id="putCallRatio" class="text-2xl font-bold">-</p>
            </div>
        </div>

        <!-- Filters and Controls -->
        <div class="mb-4 flex justify-between items-center">
            <div class="flex space-x-4">
                <label class="inline-flex items-center">
                    <input type="checkbox" id="showOnlyITM" class="form-checkbox">
                    <span class="ml-2">Show only ITM</span>
                </label>
                <label class="inline-flex items-center">
                    <input type="checkbox" id="highlightMaxOI" class="form-checkbox">
                    <span class="ml-2">Highlight Max OI</span>
                </label>
                <label class="inline-flex items-center">
                    <input type="checkbox" id="highlightMaxPain" class="form-checkbox" checked>
                    <span class="ml-2">Highlight Max Pain</span>
                </label>
            </div>
            <div class="flex space-x-2">
                <input type="text" id="searchStrike" placeholder="Search Strike Price" class="border rounded px-3 py-1">
                <button id="exportStockCSV" class="bg-blue-600 text-white px-4 py-1 rounded hover:bg-blue-700">
                    📊 Export CSV
                </button>
            </div>
        </div>
        
        <!-- Options Chain Table -->
        <div class="overflow-x-auto">
            <table>
                <thead>
                    <tr>
                        <th colspan="6" class="text-center calls">CALL OPTIONS</th>
                        <th class="text-center center-column">Strike Price</th>
                        <th colspan="6" class="text-center puts">PUT OPTIONS</th>
                    </tr>
                    <tr>
                        <th class="calls">OI</th>
                        <th class="calls">Chg OI</th>
                        <th class="calls">Volume</th>
                        <th class="calls">IV %</th>
                        <th class="calls">LTP</th>
                        <th class="calls">Chg %</th>
                        <th class="center-column text-center">Strike</th>
                        <th class="puts">Chg %</th>
                        <th class="puts">LTP</th>
                        <th class="puts">IV %</th>
                        <th class="puts">Volume</th>
                        <th class="puts">Chg OI</th>
                        <th class="puts">OI</th>
                    </tr>
                </thead>
                <tbody id="stockOptionsData">
                    <!-- Data rows will be inserted here by JavaScript -->
                </tbody>
            </table>
        </div>
        
        <div class="mt-4 text-sm text-gray-500 flex justify-between">
            <span>Last updated: <span id="stockLastUpdated">-</span></span>
            <span>Data Source: NSE Stock Options API</span>
        </div>
    </div>

    <script>
        let currentStockData = null;

        // Fetch stock options data
        function fetchStockOptionsData() {
            document.getElementById('stockOptionsData').innerHTML = '<tr><td colspan="13" class="text-center py-4">Loading stock options data...</td></tr>';
            document.getElementById('stockSpotPrice').textContent = 'Loading...';
            
            const symbol = document.getElementById('stockSelector').value;
            const expiry = document.getElementById('stockExpirySelector').value;
            
            if (!expiry) {
                document.getElementById('stockOptionsData').innerHTML = '<tr><td colspan="13" class="text-center py-4">Please select an expiry date</td></tr>';
                return;
            }
            
            // Use stock options API endpoint (we'll need to create this)
            fetch(`http://localhost:8000/api/stock-options-chain?symbol=${symbol}&expiry=${expiry}`)
                .then(response => {
                    if (!response.ok) throw new Error('Network response failed');
                    return response.json();
                })
                .then(data => {
                    currentStockData = data;
                    updateStockDisplay(data);
                    populateStockOptionsTable(data);
                    calculateStockStatistics(data);
                    
                    const lastUpdatedElement = document.getElementById('stockLastUpdated');
                    lastUpdatedElement.dataset.timestamp = Date.now();
                    lastUpdatedElement.textContent = 'just now';
                })
                .catch(error => {
                    console.error('Error fetching stock options data:', error);
                    document.getElementById('stockOptionsData').innerHTML = 
                        '<tr><td colspan="13" class="text-center py-4 text-red-600">Failed to load stock options data. Please try again.</td></tr>';
                });
        }

        function updateStockDisplay(data) {
            document.getElementById('stockSpotPrice').textContent = data.spotPrice?.toFixed(2) || 'N/A';
            
            // Add price change indicator if available
            const changeElement = document.getElementById('stockPriceChange');
            if (data.priceChange) {
                const changeClass = data.priceChange > 0 ? 'positive' : 'negative';
                const changeSymbol = data.priceChange > 0 ? '+' : '';
                changeElement.innerHTML = `<span class="${changeClass}">(${changeSymbol}${data.priceChange.toFixed(2)})</span>`;
            }
        }

        function populateStockOptionsTable(data) {
            const tbody = document.getElementById('stockOptionsData');
            tbody.innerHTML = '';
            
            if (!data.strikePrices || data.strikePrices.length === 0) {
                tbody.innerHTML = '<tr><td colspan="13" class="text-center py-4">No options data available</td></tr>';
                return;
            }
            
            data.strikePrices.forEach(strike => {
                const callData = data.calls[strike] || {};
                const putData = data.puts[strike] || {};
                
                const row = document.createElement('tr');
                
                // Highlight ATM row
                if (Math.abs(strike - data.spotPrice) < (data.spotPrice * 0.02)) { // Within 2% of spot
                    row.classList.add('highlight');
                }
                
                row.innerHTML = `
                    <td class="calls">${(callData.openInterest || 0).toLocaleString()}</td>
                    <td class="calls ${(callData.changeinOpenInterest || 0) > 0 ? 'positive' : 'negative'}">${(callData.changeinOpenInterest || 0).toLocaleString()}</td>
                    <td class="calls">${(callData.volume || 0).toLocaleString()}</td>
                    <td class="calls">${callData.iv?.toFixed(2) || '-'}%</td>
                    <td class="calls">${callData.lastPrice?.toFixed(2) || '-'}</td>
                    <td class="calls ${(callData.change || 0) > 0 ? 'positive' : 'negative'}">${callData.change?.toFixed(2) || '-'}%</td>
                    <td class="center-column text-center">${strike.toLocaleString()}</td>
                    <td class="puts ${(putData.change || 0) > 0 ? 'positive' : 'negative'}">${putData.change?.toFixed(2) || '-'}%</td>
                    <td class="puts">${putData.lastPrice?.toFixed(2) || '-'}</td>
                    <td class="puts">${putData.iv?.toFixed(2) || '-'}%</td>
                    <td class="puts">${(putData.volume || 0).toLocaleString()}</td>
                    <td class="puts ${(putData.changeinOpenInterest || 0) > 0 ? 'positive' : 'negative'}">${(putData.changeinOpenInterest || 0).toLocaleString()}</td>
                    <td class="puts">${(putData.openInterest || 0).toLocaleString()}</td>
                `;
                
                tbody.appendChild(row);
            });
        }

        function calculateStockStatistics(data) {
            if (!data.strikePrices) return;
            
            let totalCallOI = 0;
            let totalPutOI = 0;
            let maxPainStrike = 0;
            let minPain = Infinity;
            
            // Calculate totals and max pain
            data.strikePrices.forEach(strike => {
                const callOI = data.calls[strike]?.openInterest || 0;
                const putOI = data.puts[strike]?.openInterest || 0;
                
                totalCallOI += callOI;
                totalPutOI += putOI;
                
                // Simple max pain calculation (strike with minimum total pain)
                const callPain = data.strikePrices.reduce((pain, s) => {
                    return s < strike ? pain + (data.calls[s]?.openInterest || 0) * (strike - s) : pain;
                }, 0);
                
                const putPain = data.strikePrices.reduce((pain, s) => {
                    return s > strike ? pain + (data.puts[s]?.openInterest || 0) * (s - strike) : pain;
                }, 0);
                
                const totalPain = callPain + putPain;
                if (totalPain < minPain) {
                    minPain = totalPain;
                    maxPainStrike = strike;
                }
            });
            
            // Update statistics display
            document.getElementById('maxPainStrike').textContent = maxPainStrike.toLocaleString();
            document.getElementById('totalCallOI').textContent = totalCallOI.toLocaleString();
            document.getElementById('totalPutOI').textContent = totalPutOI.toLocaleString();
            document.getElementById('putCallRatio').textContent = totalCallOI > 0 ? (totalPutOI / totalCallOI).toFixed(2) : 'N/A';
        }

        // Load stock expiry dates
        function loadStockExpiryDates() {
            const symbol = document.getElementById('stockSelector').value;
            const expirySelector = document.getElementById('stockExpirySelector');
            
            expirySelector.innerHTML = '<option value="">Loading...</option>';
            
            fetch(`http://localhost:8000/api/stock-expiry-dates?symbol=${symbol}`)
                .then(response => {
                    if (!response.ok) throw new Error('Network response failed');
                    return response.json();
                })
                .then(dates => {
                    expirySelector.innerHTML = '';
                    dates.forEach(date => {
                        const option = document.createElement('option');
                        option.value = date;
                        option.textContent = date;
                        expirySelector.appendChild(option);
                    });
                    
                    if (dates.length > 0) {
                        expirySelector.value = dates[0];
                        fetchStockOptionsData();
                    }
                })
                .catch(error => {
                    console.error('Error loading stock expiry dates:', error);
                    expirySelector.innerHTML = '<option value="">Failed to load dates</option>';
                });
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            loadStockExpiryDates();
            
            // Event listeners
            document.getElementById('stockSelector').addEventListener('change', loadStockExpiryDates);
            document.getElementById('stockExpirySelector').addEventListener('change', fetchStockOptionsData);
            document.getElementById('stockRefreshButton').addEventListener('click', fetchStockOptionsData);
            
            // Update timestamp every second
            setInterval(() => {
                const lastUpdatedElement = document.getElementById('stockLastUpdated');
                if (lastUpdatedElement.dataset.timestamp) {
                    const timestamp = parseInt(lastUpdatedElement.dataset.timestamp);
                    const seconds = Math.floor((Date.now() - timestamp) / 1000);
                    
                    if (seconds < 60) {
                        lastUpdatedElement.textContent = `${seconds} seconds ago`;
                    } else if (seconds < 3600) {
                        lastUpdatedElement.textContent = `${Math.floor(seconds / 60)} minutes ago`;
                    } else {
                        lastUpdatedElement.textContent = `${Math.floor(seconds / 3600)} hours ago`;
                    }
                }
            }, 1000);
        });
    </script>
</body>
</html>
