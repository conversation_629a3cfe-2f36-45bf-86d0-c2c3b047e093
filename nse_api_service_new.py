import datetime
import sys
import time
import requests
import pandas as pd
import random
import json
import traceback
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional, Union

app = FastAPI(title="NSE Options Chain API", description="API for NSE Options Chain data")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with your frontend domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# NSE URLs and headers
url_oc = "https://www.nseindia.com/option-chain"
url_index = "https://www.nseindia.com/api/option-chain-indices?symbol="
url_stock = "https://www.nseindia.com/api/option-chain-equities?symbol="
url_symbols = "https://www.nseindia.com/api/underlying-information"

headers = {
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'accept-language': 'en,gu;q=0.9,hi;q=0.8',
    'accept-encoding': 'gzip, deflate, br',
    'accept': '*/*',
    'referer': 'https://www.nseindia.com/option-chain',
    'x-requested-with': 'XMLHttpRequest',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin'
}

# Create a session to maintain cookies
session = requests.Session()
cookies = {}
last_request_time = 0
request_delay = 1  # Delay between requests in seconds

# Default indices
DEFAULT_INDICES = ["NIFTY", "BANKNIFTY", "FINNIFTY", "MIDCPNIFTY"]

def is_index(symbol):
    """Determine if a symbol is an index or equity"""
    return symbol in ["NIFTY", "BANKNIFTY", "FINNIFTY", "MIDCPNIFTY"]

# Function to initialize or refresh the session
def refresh_session():
    global session, cookies, last_request_time
    try:
        print("Refreshing NSE session and cookies...")
        session = requests.Session()
        session.headers.update(headers)
        
        print("Step 1: Visiting NSE homepage...")
        home_response = session.get("https://www.nseindia.com/", timeout=15)
        if home_response.status_code != 200:
            print(f"Failed to access NSE homepage: {home_response.status_code}")
            return False
            
        time.sleep(2)
        print("Step 2: Visiting option chain page...")
        oc_response = session.get(url_oc, timeout=15)
        if oc_response.status_code != 200:
            print(f"Failed to access option chain page: {oc_response.status_code}")
            return False
            
        cookies = dict(session.cookies)
        print(f"Session refreshed successfully. Got {len(cookies)} cookies.")
        last_request_time = time.time()
        return True
    except Exception as err:
        print(f"Error refreshing session: {err}")
        traceback.print_exc()
        return False

@app.get("/api/options-chain/{symbol}")
async def get_options_chain(symbol: str, expiry: str = None):
    """Get options chain data for a symbol"""
    try:
        # Ensure we have valid cookies
        if not cookies:
            if not refresh_session():
                sample_data = generate_sample_options_chain(symbol, expiry)
                sample_data["isLiveData"] = False
                sample_data["message"] = "Using sample data. No valid session."
                return sample_data
        
        # Use appropriate URL based on symbol type
        base_url = url_index if is_index(symbol) else url_stock
        url = base_url + symbol
        
        # If no expiry provided, get the next available expiry
        if not expiry:
            expiry_dates = await get_expiry_dates(symbol)
            if not expiry_dates:
                sample_data = generate_sample_options_chain(symbol, None)
                sample_data["isLiveData"] = False
                sample_data["message"] = "Using sample data. No expiry dates available."
                return sample_data
            expiry = expiry_dates[0]
        
        # Make the request with a delay
        print(f"Fetching options chain for {symbol} from URL: {url}")
        response = session.get(url, headers=headers, timeout=10, cookies=cookies)
        
        # Check if we got a valid response
        if response.status_code != 200:
            print(f"Error fetching options chain: Status code {response.status_code}")
            if response.status_code == 401 or response.status_code == 403:
                if refresh_session():
                    response = session.get(url, headers=headers, timeout=10, cookies=cookies)
                else:
                    sample_data = generate_sample_options_chain(symbol, expiry)
                    sample_data["isLiveData"] = False
                    sample_data["message"] = "Using sample data. Unable to fetch live data from NSE."
                    return sample_data
        
        print(f"Response encoding: {response.encoding}")
        print(f"Content-Type: {response.headers.get('content-type', 'N/A')}")
        print(f"Content-Encoding: {response.headers.get('content-encoding', 'N/A')}")
        
        # Handle response decompression
        content_encoding = response.headers.get('content-encoding', '').lower()
        text_content = None
        
        try:
            if content_encoding == 'gzip':
                import gzip
                decompressed = gzip.decompress(response.content)
                text_content = decompressed.decode('utf-8')
                print("Successfully decompressed gzip content")
            elif content_encoding == 'br':
                try:
                    import brotli
                    decompressed = brotli.decompress(response.content)
                    text_content = decompressed.decode('utf-8')
                    print("Successfully decompressed brotli content")
                except ImportError:
                    print("Brotli library not available. Install with: pip install brotli")
                    text_content = response.text
                except Exception as e:
                    print(f"Brotli decompression failed: {e}")
                    text_content = response.text
            else:
                text_content = response.text
                print("No compression detected, using raw response text")
        except Exception as e:
            print(f"Decompression error: {e}")
            text_content = response.text
            
        if not text_content:
            raise Exception("Failed to get response content")
            
        # Try to parse the JSON
        try:
            json_data = json.loads(text_content)
            print("Successfully parsed JSON!")
        except json.JSONDecodeError as err:
            print(f"JSON decode error: {err}")
            
            # Check if response is HTML (NSE blocking us)
            if text_content.strip().startswith('<!DOCTYPE') or text_content.strip().startswith('<html'):
                print("NSE returned HTML instead of JSON - likely blocked or redirected")
                # Try to refresh session and retry once
                if refresh_session():
                    print("Retrying after session refresh...")
                    response = session.get(url, headers=headers, timeout=10, cookies=cookies)
                    if response.status_code == 200:
                        # Try decompression again
                        if content_encoding == 'br':
                            import brotli
                            decompressed = brotli.decompress(response.content)
                            text_content = decompressed.decode('utf-8')
                        elif content_encoding == 'gzip':
                            import gzip
                            decompressed = gzip.decompress(response.content)
                            text_content = decompressed.decode('utf-8')
                        else:
                            text_content = response.text
                        json_data = json.loads(text_content)
                    else:
                        raise Exception(f"Retry failed with status code: {response.status_code}")
                else:
                    sample_data = generate_sample_options_chain(symbol, expiry)
                    sample_data["isLiveData"] = False
                    sample_data["message"] = "Using sample data. Session refresh failed."
                    return sample_data
            else:
                raise
        
        # Process the data
        records = json_data.get('records', {})
        filtered_data = [item for item in records.get('data', []) if item.get('expiryDate') == expiry]
        
        # If no data for this expiry, return sample data
        if not filtered_data:
            print(f"No data found for {symbol} with expiry {expiry}")
            sample_data = generate_sample_options_chain(symbol, expiry)
            sample_data["isLiveData"] = False
            sample_data["message"] = f"No data found for {symbol} with expiry {expiry}. Using sample data."
            return sample_data
        
        # Get the exact spot price without rounding
        spot_price = records.get('underlyingValue', 0)
        timestamp = records.get('timestamp', '')
        
        # Extract strike prices - these are already properly rounded by NSE
        strike_prices = sorted(list(set(item.get('strikePrice', 0) for item in filtered_data)))
        
        # Process call and put data
        calls = {}
        puts = {}
        
        for item in filtered_data:
            strike = item.get('strikePrice', 0)
            
            if 'CE' in item:
                ce_data = item['CE']
                calls[strike] = {
                    'openInterest': ce_data.get('openInterest', 0),
                    'changeinOpenInterest': ce_data.get('changeinOpenInterest', 0),
                    'totalTradedVolume': ce_data.get('totalTradedVolume', 0),
                    'volume': ce_data.get('totalTradedVolume', 0),
                    'impliedVolatility': ce_data.get('impliedVolatility', 0),
                    'iv': ce_data.get('impliedVolatility', 0),
                    'lastPrice': ce_data.get('lastPrice', 0),
                    'change': ce_data.get('change', 0),
                    'bidQty': ce_data.get('bidQty', 0),
                    'bidprice': ce_data.get('bidprice', 0),
                    'askPrice': ce_data.get('askPrice', 0),
                    'askQty': ce_data.get('askQty', 0)
                }
            
            if 'PE' in item:
                pe_data = item['PE']
                puts[strike] = {
                    'openInterest': pe_data.get('openInterest', 0),
                    'changeinOpenInterest': pe_data.get('changeinOpenInterest', 0),
                    'totalTradedVolume': pe_data.get('totalTradedVolume', 0),
                    'volume': pe_data.get('totalTradedVolume', 0),
                    'impliedVolatility': pe_data.get('impliedVolatility', 0),
                    'iv': pe_data.get('impliedVolatility', 0),
                    'lastPrice': pe_data.get('lastPrice', 0),
                    'change': pe_data.get('change', 0),
                    'bidQty': pe_data.get('bidQty', 0),
                    'bidprice': pe_data.get('bidprice', 0),
                    'askPrice': pe_data.get('askPrice', 0),
                    'askQty': pe_data.get('askQty', 0)
                }
        
        return {
            "symbol": symbol,
            "spotPrice": spot_price,
            "expiryDate": expiry,
            "timestamp": timestamp,
            "strikePrices": strike_prices,
            "calls": calls,
            "puts": puts,
            "isLiveData": True,
            "message": None
        }
    except Exception as err:
        print(f"Error fetching options chain: {err}")
        traceback.print_exc()
        # Return some default options chain data in case of error
        sample_data = generate_sample_options_chain(symbol, expiry)
        sample_data["isLiveData"] = False
        sample_data["message"] = f"Error fetching options chain: {str(err)}. Using sample data."
        return sample_data

@app.get("/api/stock-expiry-dates")
async def get_stock_expiry_dates(symbol: str):
    """Get available expiry dates for a stock"""
    try:
        # Ensure we have valid cookies
        if not cookies:
            if not refresh_session():
                return generate_default_stock_expiry_dates(symbol)

        url = url_stock + symbol
        print(f"Fetching stock expiry dates for {symbol} from URL: {url}")

        # Make the request with a delay
        response = session.get(url, headers=headers, timeout=10, cookies=cookies)

        # Check if we got a valid response
        if response.status_code != 200:
            print(f"Error fetching stock expiry dates: Status code {response.status_code}")
            if response.status_code == 401 or response.status_code == 403:
                if refresh_session():
                    response = session.get(url, headers=headers, timeout=10, cookies=cookies)
                else:
                    return generate_default_stock_expiry_dates(symbol)

        # Handle response decompression
        content_encoding = response.headers.get('content-encoding', '').lower()
        text_content = None

        try:
            if content_encoding == 'gzip':
                import gzip
                decompressed = gzip.decompress(response.content)
                text_content = decompressed.decode('utf-8')
                print("Successfully decompressed gzip content")
            elif content_encoding == 'br':
                try:
                    import brotli
                    decompressed = brotli.decompress(response.content)
                    text_content = decompressed.decode('utf-8')
                    print("Successfully decompressed brotli content")
                except ImportError:
                    print("Brotli library not available. Install with: pip install brotli")
                    text_content = response.text
                except Exception as e:
                    print(f"Brotli decompression failed: {e}")
                    text_content = response.text
            else:
                text_content = response.text
                print("No compression detected, using raw response text")
        except Exception as e:
            print(f"Decompression error: {e}")
            text_content = response.text

        if not text_content:
            raise Exception("Failed to get response content")

        # Try to parse the JSON
        try:
            json_data = json.loads(text_content)
            print("Successfully parsed JSON!")
        except json.JSONDecodeError as err:
            print(f"JSON decode error: {err}")
            if text_content.strip().startswith('<!DOCTYPE') or text_content.strip().startswith('<html'):
                print("NSE returned HTML instead of JSON - likely blocked or redirected")
                if refresh_session():
                    response = session.get(url, headers=headers, timeout=10, cookies=cookies)
                    if response.status_code == 200:
                        if content_encoding == 'br':
                            decompressed = brotli.decompress(response.content)
                            text_content = decompressed.decode('utf-8')
                        elif content_encoding == 'gzip':
                            decompressed = gzip.decompress(response.content)
                            text_content = decompressed.decode('utf-8')
                        else:
                            text_content = response.text
                        json_data = json.loads(text_content)
                    else:
                        return generate_default_stock_expiry_dates(symbol)
                else:
                    return generate_default_stock_expiry_dates(symbol)
            else:
                return generate_default_stock_expiry_dates(symbol)

        expiry_dates = json_data.get('records', {}).get('expiryDates', [])
        if not expiry_dates:
            print(f"No expiry dates found for {symbol}")
            return generate_default_stock_expiry_dates(symbol)

        print(f"Found {len(expiry_dates)} expiry dates for {symbol}")
        return expiry_dates

    except Exception as err:
        print(f"Error fetching stock expiry dates: {err}")
        traceback.print_exc()
        return generate_default_stock_expiry_dates(symbol)

def generate_default_stock_expiry_dates(symbol):
    """Generate default expiry dates for stocks (monthly expiries)"""
    today = datetime.datetime.now()

    # Find the last Thursday of current month
    last_day = datetime.datetime(today.year, today.month + 1, 1) - datetime.timedelta(days=1)
    offset = (3 - last_day.weekday()) % 7
    last_thursday = last_day - datetime.timedelta(days=offset)

    # Generate 3 monthly expiries
    expiries = []
    current = last_thursday
    for _ in range(3):
        expiries.append(current.strftime("%d-%b-%Y"))
        # Move to next month
        if current.month == 12:
            next_month = datetime.datetime(current.year + 1, 1, 1)
        else:
            next_month = datetime.datetime(current.year, current.month + 1, 1)
        last_day = datetime.datetime(next_month.year, next_month.month + 1, 1) - datetime.timedelta(days=1)
        offset = (3 - last_day.weekday()) % 7
        current = last_day - datetime.timedelta(days=offset)

    return expiries

# Add spot price cache
spot_price_cache = {}
spot_price_cache_time = {}
CACHE_EXPIRY_SECONDS = 60  # Cache expires after 60 seconds

def generate_sample_options_chain(symbol, expiry):
    """Generate sample options chain data for development/fallback"""
    
    # Get live spot price first, if available
    spot_price, is_live_spot = get_live_spot_price(symbol) if symbol else (0, False)
    
    # If we couldn't get a live price, use default values
    if not spot_price:
        stock_fallback_prices = {
            "INFY": 1605.00,  # Updated to current market price
            "TCS": 3250.00,
            "RELIANCE": 2450.00,
            "HDFCBANK": 1650.00,
            "ICICIBANK": 1150.00,
            "SBIN": 820.00,
            "BHARTIARTL": 1520.00,
            "ITC": 465.00,
            "KOTAKBANK": 1750.00,
            "LT": 3650.00
        }
        spot_price = stock_fallback_prices.get(symbol, 1000.00)

    # Generate strike prices around the spot price with appropriate intervals
    if symbol in ["NIFTY", "BANKNIFTY", "FINNIFTY", "MIDCPNIFTY"]:
        # Index specific intervals
        if symbol == "BANKNIFTY":
            interval = 100
        else:
            interval = 50
    else:
        # Stock specific intervals based on price
        if spot_price < 500:
            interval = 2.5
        elif spot_price < 1000:
            interval = 5
        elif spot_price < 2000:
            interval = 10
        else:
            interval = 25

    base = round(spot_price / interval) * interval
    strike_prices = [base + (i - 10) * interval for i in range(21)]

    # Generate call and put data
    calls = {}
    puts = {}

    for strike in strike_prices:
        # Calculate distance from spot as percentage for realistic values
        distance_pct = abs((strike - spot_price) / spot_price)

        # Call option data
        call_price = max(0.05, spot_price - strike + random.uniform(-50, 50))
        volume_val = int(random.uniform(100, 5000) * (1 - distance_pct))
        iv_val = max(5, 25 + (distance_pct * 50) + random.uniform(-5, 5))
        calls[strike] = {
            'openInterest': int(random.uniform(1000, 20000) * (1 - distance_pct)),
            'changeinOpenInterest': int(random.uniform(-1000, 1000)),
            'totalTradedVolume': volume_val,
            'volume': volume_val,
            'impliedVolatility': iv_val,
            'iv': iv_val,
            'lastPrice': max(0.05, call_price),
            'change': random.uniform(-5, 5),
            'bidQty': int(random.uniform(50, 500)),
            'bidprice': max(0.05, call_price - random.uniform(0.5, 2)),
            'askPrice': max(0.05, call_price + random.uniform(0.5, 2)),
            'askQty': int(random.uniform(50, 500))
        }

        # Put option data
        put_price = max(0.05, strike - spot_price + random.uniform(-50, 50))
        volume_val = int(random.uniform(100, 5000) * (1 - distance_pct))
        iv_val = max(5, 25 + (distance_pct * 50) + random.uniform(-5, 5))
        puts[strike] = {
            'openInterest': int(random.uniform(1000, 20000) * (1 - distance_pct)),
            'changeinOpenInterest': int(random.uniform(-1000, 1000)),
            'totalTradedVolume': volume_val,
            'volume': volume_val,
            'impliedVolatility': iv_val,
            'iv': iv_val,
            'lastPrice': max(0.05, put_price),
            'change': random.uniform(-5, 5),
            'bidQty': int(random.uniform(50, 500)),
            'bidprice': max(0.05, put_price - random.uniform(0.5, 2)),
            'askPrice': max(0.05, put_price + random.uniform(0.5, 2)),
            'askQty': int(random.uniform(50, 500))
        }

    return {
        "symbol": symbol,
        "spotPrice": spot_price,
        "expiryDate": expiry,
        "timestamp": datetime.datetime.now().strftime("%d-%b-%Y %H:%M:%S"),
        "strikePrices": strike_prices,
        "calls": calls,
        "puts": puts,
        "isLiveData": False,
        "spotPriceIsLive": is_live_spot
    }

def get_live_spot_price(symbol):
    """Get live spot price for an index or equity with caching"""
    current_time = time.time()
    
    # Check if we have a cached value that's still valid
    if symbol in spot_price_cache and symbol in spot_price_cache_time:
        if current_time - spot_price_cache_time[symbol] < CACHE_EXPIRY_SECONDS:
            return spot_price_cache[symbol], True  # True indicates live data (from cache)
    
    try:
        # Ensure we have valid cookies
        if not cookies or len(cookies) == 0:
            refresh_session()
        
        # Use appropriate URL based on symbol type
        url = (url_index if is_index(symbol) else url_stock) + symbol
        print(f"Fetching spot price for {symbol} from URL: {url}")
        
        # Make the request with a delay
        response = session.get(url, headers=headers, timeout=10, cookies=cookies)
        
        # Check if we got a valid response
        if response.status_code != 200:
            print(f"Error fetching spot price: Status code {response.status_code}")
            if response.status_code == 401 or response.status_code == 403:
                if refresh_session():
                    response = session.get(url, headers=headers, timeout=10, cookies=cookies)
                else:
                    return 0, False
        
        # Handle response decompression
        content_encoding = response.headers.get('content-encoding', '').lower()
        text_content = None
        
        try:
            if content_encoding == 'gzip':
                import gzip
                decompressed = gzip.decompress(response.content)
                text_content = decompressed.decode('utf-8')
            elif content_encoding == 'br':
                try:
                    import brotli
                    decompressed = brotli.decompress(response.content)
                    text_content = decompressed.decode('utf-8')
                except ImportError:
                    print("Brotli library not available. Install with: pip install brotli")
                    text_content = response.text
                except Exception as e:
                    print(f"Brotli decompression failed: {e}")
                    text_content = response.text
            else:
                text_content = response.text

            json_data = json.loads(text_content)
            records = json_data.get('records', {})
            
            # Get spot price differently for indices vs equities
            if is_index(symbol):
                spot_price = records.get('underlyingValue', 0)
            else:
                # For equities, try getting the underlying value first, then stocPrice if that's not available
                spot_price = records.get('underlyingValue', records.get('stocPrice', records.get('spotPrice', 0)))
            
            if not spot_price:
                print(f"Warning: Got zero spot price for {symbol}")
                return 0, False
            
            # Update cache
            spot_price_cache[symbol] = spot_price
            spot_price_cache_time[symbol] = current_time
            
            return spot_price, True  # True indicates live data
            
        except Exception as err:
            print(f"Error processing spot price data: {err}")
            traceback.print_exc()
            return 0, False
            
    except Exception as err:
        print(f"Error fetching live spot price: {err}")
        traceback.print_exc()
        return 0, False

# Add a health check endpoint
@app.get("/api/health")
async def health_check():
    """Health check endpoint to verify if the service is running"""
    return {"status": "healthy", "timestamp": datetime.datetime.now().isoformat()}

if __name__ == "__main__":
    import uvicorn
    print("Starting NSE API Service...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
