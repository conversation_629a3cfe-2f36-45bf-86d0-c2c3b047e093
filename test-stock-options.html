<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Stock Options API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .controls select, .controls button {
            margin: 5px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .controls button {
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        .controls button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .data-display {
            margin-top: 20px;
        }
        .data-section {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .data-section h3 {
            margin-top: 0;
            color: #333;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        th {
            background-color: #f2f2f2;
        }
        .calls { background-color: rgba(34, 197, 94, 0.1); }
        .puts { background-color: rgba(239, 68, 68, 0.1); }
        .center-column { background-color: #f8fafc; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Stock Options API Test</h1>
        
        <div class="controls">
            <label>
                Stock Symbol:
                <select id="stockSymbol">
                    <option value="INFY">INFY - Infosys</option>
                    <option value="TCS">TCS - Tata Consultancy</option>
                    <option value="RELIANCE">RELIANCE - Reliance Industries</option>
                    <option value="HDFCBANK">HDFCBANK - HDFC Bank</option>
                    <option value="ICICIBANK">ICICIBANK - ICICI Bank</option>
                </select>
            </label>
            
            <label>
                Expiry Date:
                <select id="expiryDate">
                    <option value="">Loading...</option>
                </select>
            </label>
            
            <button onclick="loadExpiryDates()">🔄 Load Expiry Dates</button>
            <button onclick="loadOptionsChain()">📊 Load Options Chain</button>
            <button onclick="testAPI()">🧪 Test All APIs</button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div class="data-display">
            <div class="data-section">
                <h3>📅 Expiry Dates</h3>
                <div id="expiryDatesResult">Click "Load Expiry Dates" to test</div>
            </div>
            
            <div class="data-section">
                <h3>📈 Stock Options Chain</h3>
                <div id="optionsChainResult">Click "Load Options Chain" to test</div>
            </div>
            
            <div class="data-section">
                <h3>📊 Options Table Preview</h3>
                <div id="optionsTableResult">Options data will appear here</div>
            </div>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'loading') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }

        function hideStatus() {
            document.getElementById('status').style.display = 'none';
        }

        async function loadExpiryDates() {
            const symbol = document.getElementById('stockSymbol').value;
            showStatus(`Loading expiry dates for ${symbol}...`);
            
            try {
                const response = await fetch(`http://localhost:8000/api/stock-expiry-dates?symbol=${symbol}`);
                const data = await response.json();
                
                // Update expiry dropdown
                const expirySelect = document.getElementById('expiryDate');
                expirySelect.innerHTML = '';
                data.forEach(date => {
                    const option = document.createElement('option');
                    option.value = date;
                    option.textContent = date;
                    expirySelect.appendChild(option);
                });
                
                // Display result
                document.getElementById('expiryDatesResult').innerHTML = `
                    <strong>Found ${data.length} expiry dates:</strong>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
                showStatus(`✅ Successfully loaded ${data.length} expiry dates`, 'success');
                setTimeout(hideStatus, 3000);
            } catch (error) {
                document.getElementById('expiryDatesResult').innerHTML = `
                    <strong style="color: red;">Error:</strong> ${error.message}
                `;
                showStatus(`❌ Error loading expiry dates: ${error.message}`, 'error');
            }
        }

        async function loadOptionsChain() {
            const symbol = document.getElementById('stockSymbol').value;
            const expiry = document.getElementById('expiryDate').value;
            
            if (!expiry) {
                showStatus('❌ Please select an expiry date first', 'error');
                return;
            }
            
            showStatus(`Loading options chain for ${symbol} expiry ${expiry}...`);
            
            try {
                const response = await fetch(`http://localhost:8000/api/stock-options-chain?symbol=${symbol}&expiry=${expiry}`);
                const data = await response.json();
                
                // Display result
                document.getElementById('optionsChainResult').innerHTML = `
                    <strong>Options Chain Data:</strong><br>
                    <strong>Symbol:</strong> ${data.symbol}<br>
                    <strong>Spot Price:</strong> ₹${data.spotPrice}<br>
                    <strong>Expiry:</strong> ${data.expiryDate}<br>
                    <strong>Strike Prices:</strong> ${data.strikePrices?.length || 0} strikes<br>
                    <strong>Live Data:</strong> ${data.isLiveData ? '✅ Yes' : '❌ No (Sample)'}<br>
                    ${data.message ? `<strong>Message:</strong> ${data.message}<br>` : ''}
                    <details>
                        <summary>Raw JSON Data</summary>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </details>
                `;
                
                // Create options table
                createOptionsTable(data);
                
                showStatus(`✅ Successfully loaded options chain with ${data.strikePrices?.length || 0} strikes`, 'success');
                setTimeout(hideStatus, 3000);
            } catch (error) {
                document.getElementById('optionsChainResult').innerHTML = `
                    <strong style="color: red;">Error:</strong> ${error.message}
                `;
                showStatus(`❌ Error loading options chain: ${error.message}`, 'error');
            }
        }

        function createOptionsTable(data) {
            if (!data.strikePrices || data.strikePrices.length === 0) {
                document.getElementById('optionsTableResult').innerHTML = 'No options data available';
                return;
            }

            let tableHTML = `
                <table>
                    <thead>
                        <tr>
                            <th colspan="3" class="calls">CALL OPTIONS</th>
                            <th class="center-column">Strike</th>
                            <th colspan="3" class="puts">PUT OPTIONS</th>
                        </tr>
                        <tr>
                            <th class="calls">OI</th>
                            <th class="calls">LTP</th>
                            <th class="calls">Volume</th>
                            <th class="center-column">Strike</th>
                            <th class="puts">Volume</th>
                            <th class="puts">LTP</th>
                            <th class="puts">OI</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            // Show first 10 strikes for preview
            const previewStrikes = data.strikePrices.slice(0, 10);
            
            previewStrikes.forEach(strike => {
                const callData = data.calls[strike] || {};
                const putData = data.puts[strike] || {};
                
                tableHTML += `
                    <tr>
                        <td class="calls">${(callData.openInterest || 0).toLocaleString()}</td>
                        <td class="calls">₹${(callData.lastPrice || 0).toFixed(2)}</td>
                        <td class="calls">${(callData.volume || 0).toLocaleString()}</td>
                        <td class="center-column">${strike.toLocaleString()}</td>
                        <td class="puts">${(putData.volume || 0).toLocaleString()}</td>
                        <td class="puts">₹${(putData.lastPrice || 0).toFixed(2)}</td>
                        <td class="puts">${(putData.openInterest || 0).toLocaleString()}</td>
                    </tr>
                `;
            });

            tableHTML += `
                    </tbody>
                </table>
                <p><em>Showing first 10 strikes. Total: ${data.strikePrices.length} strikes</em></p>
            `;

            document.getElementById('optionsTableResult').innerHTML = tableHTML;
        }

        async function testAPI() {
            showStatus('🧪 Running comprehensive API test...', 'loading');
            
            try {
                // Test 1: Load expiry dates
                await loadExpiryDates();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Test 2: Load options chain
                await loadOptionsChain();
                
                showStatus('✅ All API tests completed successfully!', 'success');
                setTimeout(hideStatus, 5000);
            } catch (error) {
                showStatus(`❌ API test failed: ${error.message}`, 'error');
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadExpiryDates();
        });
    </script>
</body>
</html>
