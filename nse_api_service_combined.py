import datetime
import sys
import time
import requests
import pandas as pd
import random
import json
import traceback
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional, Union
import brotli

app = FastAPI(title="NSE Options Chain API", description="API for NSE Options Chain data")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# NSE URLs and headers
url_oc = "https://www.nseindia.com/option-chain"
url_index = "https://www.nseindia.com/api/option-chain-indices?symbol="
url_stock = "https://www.nseindia.com/api/option-chain-equities?symbol="
url_symbols = "https://www.nseindia.com/api/underlying-information"

headers = {
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'accept-language': 'en,gu;q=0.9,hi;q=0.8',
    'accept-encoding': 'gzip, deflate, br',
    'accept': '*/*',
    'referer': 'https://www.nseindia.com/option-chain',
    'x-requested-with': 'XMLHttpRequest',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin'
}

# Create a session to maintain cookies
session = requests.Session()
session.headers.update(headers)  # Set headers at session level
cookies = {}
last_request_time = 0
request_delay = 1  # Delay between requests in seconds

# Default indices and cache settings
DEFAULT_INDICES = ["NIFTY", "BANKNIFTY", "FINNIFTY", "MIDCPNIFTY"]
spot_price_cache = {}
spot_price_cache_time = {}
CACHE_EXPIRY_SECONDS = 60  # Cache expires after 60 seconds

def is_index(symbol):
    """Determine if a symbol is an index or equity"""
    return symbol in ["NIFTY", "BANKNIFTY", "FINNIFTY", "MIDCPNIFTY"]

def refresh_session():
    """Initialize or refresh the session"""
    global session, cookies, last_request_time
    try:
        print("Refreshing NSE session and cookies...")
        session = requests.Session()
        session.headers.update(headers)

        # First visit the homepage to get initial cookies
        print("Step 1: Visiting NSE homepage...")
        home_response = session.get("https://www.nseindia.com/", timeout=15)
        if home_response.status_code != 200:
            print(f"Failed to access NSE homepage: {home_response.status_code}")
            return False

        # Wait before visiting option chain page
        time.sleep(2)
        print("Step 2: Visiting option chain page...")
        oc_response = session.get(url_oc, timeout=15)
        if oc_response.status_code != 200:
            print(f"Failed to access option chain page: {oc_response.status_code}")
            return False

        cookies = dict(session.cookies)
        print(f"Session refreshed successfully. Got {len(cookies)} cookies.")
        print(f"Cookies: {list(cookies.keys())}")
        last_request_time = time.time()
        return True
    except Exception as err:
        print(f"Error refreshing session: {err}")
        traceback.print_exc()
        return False

def handle_response_content(response):
    """Handle response decompression and JSON parsing"""
    try:
        content_encoding = response.headers.get('content-encoding', '').lower()
        text_content = None

        if content_encoding == 'gzip':
            import gzip
            decompressed = gzip.decompress(response.content)
            text_content = decompressed.decode('utf-8')
            print("Successfully decompressed gzip content")
        elif content_encoding == 'br':
            try:
                decompressed = brotli.decompress(response.content)
                text_content = decompressed.decode('utf-8')
                print("Successfully decompressed brotli content")
            except brotli.error as e:
                print(f"Brotli decompression failed: {e}")
                text_content = response.text
            except Exception as e:
                print(f"Brotli decompression failed: {e}")
                text_content = response.text
        else:
            text_content = response.text
            print("Using raw response text")

        if not text_content:
            raise Exception("Failed to get response content")

        # Parse JSON with retry on HTML response
        try:
            data = json.loads(text_content)
            return data
        except json.JSONDecodeError as err:
            print(f"JSON decode error: {err}")
            if text_content.strip().startswith(('<!DOCTYPE', '<html')):
                print("Got HTML response - session likely expired")
                raise Exception("Session expired")
            raise
    except Exception as e:
        print(f"Error processing response: {e}")
        raise

def get_live_spot_price(symbol):
    """Get live spot price for an index or equity with caching"""
    current_time = time.time()
    
    if symbol in spot_price_cache and symbol in spot_price_cache_time:
        if current_time - spot_price_cache_time[symbol] < CACHE_EXPIRY_SECONDS:
            return spot_price_cache[symbol], True
    
    try:
        if not cookies:
            refresh_session()

        url = (url_index if is_index(symbol) else url_stock) + symbol
        print(f"Fetching spot price for {symbol} from URL: {url}")
        
        response = session.get(url, cookies=cookies, timeout=10)
        
        if response.status_code != 200:
            print(f"Error fetching spot price: Status code {response.status_code}")
            if response.status_code in (401, 403):
                if refresh_session():
                    response = session.get(url, cookies=cookies, timeout=10)
                else:
                    return 0, False
        
        json_data = handle_response_content(response)
        records = json_data.get('records', {})
        
        if is_index(symbol):
            spot_price = records.get('underlyingValue', 0)
        else:
            spot_price = records.get('underlyingValue', records.get('stockPrice', records.get('spotPrice', 0)))
        
        if spot_price:
            spot_price_cache[symbol] = spot_price
            spot_price_cache_time[symbol] = current_time
            return spot_price, True
        
        print(f"Warning: Got zero spot price for {symbol}")
        return 0, False
            
    except Exception as err:
        print(f"Error fetching live spot price: {err}")
        traceback.print_exc()
        return 0, False

@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """Ensure rate limiting between requests"""
    global last_request_time
    elapsed = time.time() - last_request_time
    if elapsed < request_delay:
        time.sleep(request_delay - elapsed)
    last_request_time = time.time()
    return await call_next(request)

@app.get("/")
async def root():
    return {"message": "NSE Options Chain API is running"}

@app.get("/api/indices")
async def get_indices():
    """Get list of available indices"""
    try:
        if not cookies and not refresh_session():
            return DEFAULT_INDICES
        
        response = session.get(url_symbols, cookies=cookies, timeout=10)
        
        if response.status_code != 200:
            if response.status_code in (401, 403) and refresh_session():
                response = session.get(url_symbols, cookies=cookies, timeout=10)
            else:
                return DEFAULT_INDICES
        
        json_data = handle_response_content(response)
        indices = [item['symbol'] for item in json_data['data']['IndexList']]
        main_indices = [idx for idx in indices if idx in DEFAULT_INDICES]
        
        return main_indices or DEFAULT_INDICES
    except Exception as err:
        print(f"Error fetching indices: {err}")
        return DEFAULT_INDICES

@app.get("/api/expiry-dates")
async def get_expiry_dates(symbol: str):
    """Get available expiry dates for an index or stock"""
    try:
        if not cookies and not refresh_session():
            return generate_default_expiry_dates(symbol)
        
        url = (url_index if is_index(symbol) else url_stock) + symbol
        print(f"Fetching expiry dates for {symbol} from URL: {url}")
        
        response = session.get(url, cookies=cookies, timeout=10)
        
        if response.status_code != 200:
            if response.status_code in (401, 403) and refresh_session():
                response = session.get(url, cookies=cookies, timeout=10)
            else:
                return generate_default_expiry_dates(symbol)
        
        json_data = handle_response_content(response)
        expiry_dates = json_data.get('records', {}).get('expiryDates', [])
        
        if not expiry_dates:
            return generate_default_expiry_dates(symbol)
            
        return expiry_dates
    except Exception as err:
        print(f"Error fetching expiry dates: {err}")
        return generate_default_expiry_dates(symbol)

@app.get("/api/options-chain/{symbol}")
async def get_options_chain(symbol: str, expiry: str = None):
    """Get options chain data for a symbol (index or stock)"""
    try:
        if not cookies and not refresh_session():
            return generate_sample_chain(symbol, expiry, False)
        
        url = (url_index if is_index(symbol) else url_stock) + symbol
        
        if not expiry:
            expiry_dates = await get_expiry_dates(symbol)
            if not expiry_dates:
                return generate_sample_chain(symbol, None, False)
            expiry = expiry_dates[0]
        
        print(f"Fetching options chain for {symbol} from URL: {url}")
        response = session.get(url, cookies=cookies, timeout=10)
        
        if response.status_code != 200:
            if response.status_code in (401, 403) and refresh_session():
                response = session.get(url, cookies=cookies, timeout=10)
            else:
                return generate_sample_chain(symbol, expiry, False)
        
        json_data = handle_response_content(response)
        records = json_data.get('records', {})
        filtered_data = [item for item in records.get('data', []) if item.get('expiryDate') == expiry]
        
        if not filtered_data:
            print(f"No data found for {symbol} with expiry {expiry}")
            return generate_sample_chain(symbol, expiry, False)
        
        spot_price = records.get('underlyingValue', records.get('stockPrice', records.get('spotPrice', 0)))
        timestamp = records.get('timestamp', '')
        strike_prices = sorted(list(set(item.get('strikePrice', 0) for item in filtered_data)))
        
        calls = {}
        puts = {}
        
        for item in filtered_data:
            strike = item.get('strikePrice', 0)
            
            if 'CE' in item:
                ce_data = item['CE']
                calls[strike] = {
                    'openInterest': ce_data.get('openInterest', 0),
                    'changeinOpenInterest': ce_data.get('changeinOpenInterest', 0),
                    'totalTradedVolume': ce_data.get('totalTradedVolume', 0),
                    'volume': ce_data.get('totalTradedVolume', 0),
                    'impliedVolatility': ce_data.get('impliedVolatility', 0),
                    'iv': ce_data.get('impliedVolatility', 0),
                    'lastPrice': ce_data.get('lastPrice', 0),
                    'change': ce_data.get('change', 0),
                    'bidQty': ce_data.get('bidQty', 0),
                    'bidprice': ce_data.get('bidprice', 0),
                    'askPrice': ce_data.get('askPrice', 0),
                    'askQty': ce_data.get('askQty', 0)
                }
            
            if 'PE' in item:
                pe_data = item['PE']
                puts[strike] = {
                    'openInterest': pe_data.get('openInterest', 0),
                    'changeinOpenInterest': pe_data.get('changeinOpenInterest', 0),
                    'totalTradedVolume': pe_data.get('totalTradedVolume', 0),
                    'volume': pe_data.get('totalTradedVolume', 0),
                    'impliedVolatility': pe_data.get('impliedVolatility', 0),
                    'iv': pe_data.get('impliedVolatility', 0),
                    'lastPrice': pe_data.get('lastPrice', 0),
                    'change': pe_data.get('change', 0),
                    'bidQty': pe_data.get('bidQty', 0),
                    'bidprice': pe_data.get('bidprice', 0),
                    'askPrice': pe_data.get('askPrice', 0),
                    'askQty': pe_data.get('askQty', 0)
                }
        
        return {
            "symbol": symbol,
            "spotPrice": spot_price,
            "expiryDate": expiry,
            "timestamp": timestamp,
            "strikePrices": strike_prices,
            "calls": calls,
            "puts": puts,
            "isLiveData": True,
            "message": None
        }
    except Exception as err:
        print(f"Error fetching options chain: {err}")
        traceback.print_exc()
        return generate_sample_chain(symbol, expiry, False)

def generate_default_expiry_dates(symbol):
    """Generate default expiry dates"""
    today = datetime.datetime.now()
    
    if is_index(symbol):
        # Weekly expiries for indices
        days_until_thursday = (3 - today.weekday()) % 7
        next_thursday = today + datetime.timedelta(days=days_until_thursday)
        return [(next_thursday + datetime.timedelta(days=i*7)).strftime("%d-%b-%Y") for i in range(4)]
    else:
        # Monthly expiries for stocks
        last_day = datetime.datetime(today.year, today.month + 1, 1) - datetime.timedelta(days=1)
        offset = (3 - last_day.weekday()) % 7
        last_thursday = last_day - datetime.timedelta(days=offset)
        
        expiries = []
        current = last_thursday
        for _ in range(3):
            expiries.append(current.strftime("%d-%b-%Y"))
            if current.month == 12:
                next_month = datetime.datetime(current.year + 1, 1, 1)
            else:
                next_month = datetime.datetime(current.year, current.month + 1, 1)
            last_day = datetime.datetime(next_month.year, next_month.month + 1, 1) - datetime.timedelta(days=1)
            offset = (3 - last_day.weekday()) % 7
            current = last_day - datetime.timedelta(days=offset)
        return expiries

def generate_sample_chain(symbol, expiry, is_live_spot=False):
    """Generate sample options chain data"""
    spot_price, is_live_spot = get_live_spot_price(symbol) if symbol else (0, False)
    
    if not spot_price:
        default_prices = {
            "NIFTY": 24756.35,
            "BANKNIFTY": 55527.35,
            "FINNIFTY": 21356.75,
            "MIDCPNIFTY": 12523.45,
            "INFY": 1605.00,
            "TCS": 3250.00,
            "RELIANCE": 2450.00,
            "HDFCBANK": 1650.00,
            "ICICIBANK": 1150.00,
            "SBIN": 820.00
        }
        spot_price = default_prices.get(symbol, 1000.00)
    
    # Determine strike price interval
    if symbol == "BANKNIFTY":
        interval = 100
    elif symbol == "NIFTY":
        interval = 50
    elif symbol == "FINNIFTY":
        interval = 50
    elif spot_price < 500:
        interval = 2.5
    elif spot_price < 1000:
        interval = 5
    elif spot_price < 2000:
        interval = 10
    else:
        interval = 25

    base = round(spot_price / interval) * interval
    strike_prices = [base + (i - 10) * interval for i in range(21)]
    
    calls = {}
    puts = {}
    
    for strike in strike_prices:
        distance_pct = abs((strike - spot_price) / spot_price)
        
        # Call option
        call_price = max(0.05, spot_price - strike + random.uniform(-50, 50))
        volume = int(random.uniform(100, 5000) * (1 - distance_pct))
        iv = max(5, 25 + (distance_pct * 50) + random.uniform(-5, 5))
        
        calls[strike] = {
            'openInterest': int(random.uniform(1000, 20000) * (1 - distance_pct)),
            'changeinOpenInterest': int(random.uniform(-1000, 1000)),
            'totalTradedVolume': volume,
            'volume': volume,
            'impliedVolatility': iv,
            'iv': iv,
            'lastPrice': max(0.05, call_price),
            'change': random.uniform(-5, 5),
            'bidQty': int(random.uniform(50, 500)),
            'bidprice': max(0.05, call_price - random.uniform(0.5, 2)),
            'askPrice': max(0.05, call_price + random.uniform(0.5, 2)),
            'askQty': int(random.uniform(50, 500))
        }
        
        # Put option
        put_price = max(0.05, strike - spot_price + random.uniform(-50, 50))
        volume = int(random.uniform(100, 5000) * (1 - distance_pct))
        iv = max(5, 25 + (distance_pct * 50) + random.uniform(-5, 5))
        
        puts[strike] = {
            'openInterest': int(random.uniform(1000, 20000) * (1 - distance_pct)),
            'changeinOpenInterest': int(random.uniform(-1000, 1000)),
            'totalTradedVolume': volume,
            'volume': volume,
            'impliedVolatility': iv,
            'iv': iv,
            'lastPrice': max(0.05, put_price),
            'change': random.uniform(-5, 5),
            'bidQty': int(random.uniform(50, 500)),
            'bidprice': max(0.05, put_price - random.uniform(0.5, 2)),
            'askPrice': max(0.05, put_price + random.uniform(0.5, 2)),
            'askQty': int(random.uniform(50, 500))
        }
    
    return {
        "symbol": symbol,
        "spotPrice": spot_price,
        "expiryDate": expiry or "25-Jul-2025",
        "timestamp": datetime.datetime.now().strftime("%d-%b-%Y %H:%M:%S"),
        "strikePrices": strike_prices,
        "calls": calls,
        "puts": puts,
        "isLiveData": False,
        "spotPriceIsLive": is_live_spot,
        "message": "Using sample data"
    }

if __name__ == "__main__":
    import uvicorn
    print("Starting NSE API Service...")
    print("Available endpoints:")
    print("  GET /api/indices - Get available indices")
    print("  GET /api/expiry-dates?symbol=SYMBOL - Get expiry dates for symbol")
    print("  GET /api/options-chain/SYMBOL - Get options chain with optional expiry parameter")
    
    # Initialize session
    refresh_session()
    
    # Start server
    uvicorn.run(app, host="0.0.0.0", port=8000)
