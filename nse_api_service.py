import datetime
import sys
import time
import requests
import pandas as pd
import random
import json
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional, Union

app = FastAPI(title="NSE Options Chain API", description="API for NSE Options Chain data")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with your frontend domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# NSE URLs and headers
url_oc = "https://www.nseindia.com/option-chain"
url_index = "https://www.nseindia.com/api/option-chain-indices?symbol="
url_stock = "https://www.nseindia.com/api/option-chain-equities?symbol="
url_symbols = "https://www.nseindia.com/api/underlying-information"

headers = {
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'accept-language': 'en,gu;q=0.9,hi;q=0.8',
    'accept-encoding': 'gzip, deflate, br',
    'accept': '*/*',
    'referer': 'https://www.nseindia.com/option-chain',
    'x-requested-with': 'XMLHttpRequest',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin'
}

# Create a session to maintain cookies
session = requests.Session()
cookies = {}
last_request_time = 0
request_delay = 1  # Delay between requests in seconds

# Default indices
DEFAULT_INDICES = ["NIFTY", "BANKNIFTY", "FINNIFTY", "MIDCPNIFTY"]

# Add a cache for spot prices to avoid making too many requests
spot_price_cache = {}
spot_price_cache_time = {}
CACHE_EXPIRY_SECONDS = 60  # Cache expires after 60 seconds

# Function to initialize or refresh the session
def refresh_session():
    global session, cookies, last_request_time

    try:
        print("Refreshing NSE session and cookies...")
        session = requests.Session()

        # Set up session with proper headers
        session.headers.update(headers)

        # First visit the homepage to get initial cookies
        print("Step 1: Visiting NSE homepage...")
        home_response = session.get("https://www.nseindia.com/", timeout=15)
        print(f"Homepage response: {home_response.status_code}")

        if home_response.status_code != 200:
            print(f"Failed to access NSE homepage: {home_response.status_code}")
            print(f"Response content: {home_response.text[:200]}...")
            return False

        # Wait a bit and then visit the option chain page
        time.sleep(2)  # Increased delay
        print("Step 2: Visiting option chain page...")
        oc_response = session.get(url_oc, timeout=15)
        print(f"Option chain response: {oc_response.status_code}")

        if oc_response.status_code != 200:
            print(f"Failed to access option chain page: {oc_response.status_code}")
            print(f"Response content: {oc_response.text[:200]}...")
            return False

        cookies = dict(session.cookies)
        print(f"Session refreshed successfully. Got {len(cookies)} cookies.")
        print(f"Cookies: {list(cookies.keys())}")
        last_request_time = time.time()
        return True
    except Exception as err:
        print(f"Error refreshing session: {err}")
        import traceback
        traceback.print_exc()
        return False

# Initialize the session at startup
refresh_session()

# Middleware to handle rate limiting and session management
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    global last_request_time
    
    # Ensure we don't make requests too quickly
    elapsed = time.time() - last_request_time
    if elapsed < request_delay:
        time.sleep(request_delay - elapsed)
    
    last_request_time = time.time()
    response = await call_next(request)
    return response

@app.get("/")
async def root():
    return {"message": "NSE Options Chain API is running"}

@app.get("/api/indices")
async def get_indices():
    """Get list of available indices"""
    try:
        # Ensure we have valid cookies
        if not cookies:
            if not refresh_session():
                return DEFAULT_INDICES
        
        # Make the request with a delay
        response = session.get(url_symbols, headers=headers, timeout=10, cookies=cookies)
        
        # Check if we got a valid response
        if response.status_code != 200:
            print(f"Error fetching indices: Status code {response.status_code}")
            if response.status_code == 401 or response.status_code == 403:
                refresh_session()
                response = session.get(url_symbols, headers=headers, timeout=10, cookies=cookies)
        
        # Try to parse the JSON
        try:
            json_data = response.json()
        except Exception as err:
            print(f"Error parsing JSON: {err}")
            print(f"Response content: {response.text[:200]}...")  # Print first 200 chars
            return DEFAULT_INDICES
        
        indices = [item['symbol'] for item in json_data['data']['IndexList']]
        
        # Filter to include only the main indices we're interested in
        main_indices = [idx for idx in indices if idx in ["NIFTY", "BANKNIFTY", "FINNIFTY", "MIDCPNIFTY"]]
        
        # If no main indices found, return defaults
        if not main_indices:
            return DEFAULT_INDICES
            
        return main_indices
    except Exception as err:
        print(f"Error fetching indices: {err}")
        return DEFAULT_INDICES

@app.get("/api/expiry-dates")
async def get_expiry_dates(symbol: str):
    """Get available expiry dates for an index"""
    try:
        # Ensure we have valid cookies
        if not cookies:
            if not refresh_session():
                # Return some default expiry dates based on the index
                return generate_default_expiry_dates(symbol)
        
        url = url_index + symbol
        
        # Make the request with a delay
        response = session.get(url, headers=headers, timeout=10, cookies=cookies)
        
        # Check if we got a valid response
        if response.status_code != 200:
            print(f"Error fetching expiry dates: Status code {response.status_code}")
            if response.status_code == 401 or response.status_code == 403:
                refresh_session()
                response = session.get(url, headers=headers, timeout=10, cookies=cookies)
        
        # Try to parse the JSON
        try:
            json_data = response.json()
        except Exception as err:
            print(f"Error parsing JSON: {err}")
            print(f"Response content: {response.text[:200]}...")  # Print first 200 chars
            return generate_default_expiry_dates(symbol)
        
        expiry_dates = json_data.get('records', {}).get('expiryDates', [])
        
        # If no expiry dates found, return defaults
        if not expiry_dates:
            return generate_default_expiry_dates(symbol)
            
        return expiry_dates
    except Exception as err:
        print(f"Error fetching expiry dates: {err}")
        return generate_default_expiry_dates(symbol)

@app.get("/api/market-data")
async def get_market_data(symbol: str):
    """Get current market data for an index"""
    try:
        # Get live spot price
        spot_price, is_live_data = get_live_spot_price(symbol)
        
        return {
            "symbol": symbol,
            "spotPrice": spot_price,
            "timestamp": datetime.datetime.now().strftime("%d-%b-%Y %H:%M:%S"),
            "isLiveData": is_live_data,
            "message": None if is_live_data else "Using fallback data. Unable to fetch live data from NSE."
        }
    except Exception as err:
        print(f"Error fetching market data: {err}")
        # Return fallback values if we can't get live data
        fallback_prices = {
            "NIFTY": 24756.35,
            "BANKNIFTY": 55527.35,
            "FINNIFTY": 21356.75,
            "MIDCPNIFTY": 12523.45
        }
        return {
            "symbol": symbol,
            "spotPrice": fallback_prices.get(symbol, 20000.00),
            "timestamp": datetime.datetime.now().strftime("%d-%b-%Y %H:%M:%S"),
            "isLiveData": False,
            "message": "Using fallback data. Unable to fetch live data from NSE."
        }

@app.get("/api/options-chain/{symbol}")
async def get_options_chain(symbol: str, expiry: str = None):
    """Get options chain data for a symbol"""
    try:
        # Ensure we have valid cookies
        if not cookies:
            if not refresh_session():
                sample_data = generate_sample_options_chain(symbol, expiry)
                sample_data["isLiveData"] = False
                sample_data["message"] = "Using sample data. No valid session."
                return sample_data
        
        # Use appropriate URL based on symbol type
        base_url = url_index if is_index(symbol) else url_stock
        url = base_url + symbol
        
        # If no expiry provided, get the next available expiry
        if not expiry:
            expiry_dates = await get_expiry_dates(symbol)
            if not expiry_dates:
                sample_data = generate_sample_options_chain(symbol, None)
                sample_data["isLiveData"] = False
                sample_data["message"] = "Using sample data. No expiry dates available."
                return sample_data
            expiry = expiry_dates[0]
        
        # Make the request with a delay
        print(f"Fetching options chain for {symbol} from URL: {url}")
        response = session.get(url, headers=headers, timeout=10, cookies=cookies)

        # Check if we got a valid response
        if response.status_code != 200:
            print(f"Error fetching options chain: Status code {response.status_code}")
            if response.status_code == 401 or response.status_code == 403:
                if refresh_session():
                    response = session.get(url, headers=headers, timeout=10, cookies=cookies)
                else:
                    sample_data = generate_sample_options_chain(symbol, expiry)
                    sample_data["isLiveData"] = False
                    sample_data["message"] = "Using sample data. Unable to fetch live data from NSE."
                    return sample_data

        # Try to parse the JSON - let requests handle decompression
        try:
            json_data = response.json()
            print(f"Successfully parsed index options JSON for {symbol}")
        except Exception as err:
            print(f"Error parsing index options JSON: {err}")
            print(f"Response status: {response.status_code}")
            print(f"Response headers: {dict(response.headers)}")
            print(f"Response content preview: {response.text[:500]}...")

            # Check if response is HTML (NSE blocking us)
            if response.text.strip().startswith('<!DOCTYPE') or response.text.strip().startswith('<html'):
                print("NSE returned HTML instead of JSON - likely blocked or redirected")
                # Try to refresh session and retry once
                if refresh_session():
                    print("Retrying index options after session refresh...")
                    time.sleep(2)
                    retry_response = session.get(url, headers=headers, timeout=10, cookies=cookies)
                    try:
                        json_data = retry_response.json()
                        print("Index options retry successful!")
                    except:
                        print("Index options retry also failed, using sample data")
                        sample_data = generate_sample_options_chain(symbol, expiry)
                        sample_data["isLiveData"] = False
                        sample_data["message"] = "Using sample data. NSE blocked requests (returned HTML)."
                        return sample_data
                else:
                    sample_data = generate_sample_options_chain(symbol, expiry)
                    sample_data["isLiveData"] = False
                    sample_data["message"] = "Using sample data. Unable to refresh NSE session."
                    return sample_data
            else:
                # Return sample data for other parsing errors
                sample_data = generate_sample_options_chain(symbol, expiry)
                sample_data["isLiveData"] = False
                sample_data["message"] = f"Using sample data. Unable to parse JSON response from NSE. Error: {str(err)}"
                return sample_data
                
        # Process the data
        records = json_data.get('records', {})
        filtered_data = [item for item in records.get('data', []) if item.get('expiryDate') == expiry]
        
        # If no data for this expiry, return sample data
        if not filtered_data:
            print(f"No data found for {symbol} with expiry {expiry}")
            sample_data = generate_sample_options_chain(symbol, expiry)
            sample_data["isLiveData"] = False
            sample_data["message"] = f"No data found for {symbol} with expiry {expiry}. Using sample data."
            return sample_data
        
        # Get the exact spot price without rounding
        spot_price = records.get('underlyingValue', 0)
        timestamp = records.get('timestamp', '')
        
        # Extract strike prices - these are already properly rounded by NSE
        strike_prices = sorted(list(set(item.get('strikePrice', 0) for item in filtered_data)))
        
        # Process call and put data
        calls = {}
        puts = {}
        
        for item in filtered_data:
            strike = item.get('strikePrice', 0)
            
            if 'CE' in item:
                ce_data = item['CE']
                calls[strike] = {
                    'openInterest': ce_data.get('openInterest', 0),  # Use raw OI value from NSE
                    'changeinOpenInterest': ce_data.get('changeinOpenInterest', 0),
                    'totalTradedVolume': ce_data.get('totalTradedVolume', 0),
                    'volume': ce_data.get('totalTradedVolume', 0),  # Map for frontend compatibility
                    'impliedVolatility': ce_data.get('impliedVolatility', 0),
                    'iv': ce_data.get('impliedVolatility', 0),  # Map for frontend compatibility
                    'lastPrice': ce_data.get('lastPrice', 0),
                    'change': ce_data.get('change', 0),
                    'bidQty': ce_data.get('bidQty', 0),
                    'bidprice': ce_data.get('bidprice', 0),
                    'askPrice': ce_data.get('askPrice', 0),
                    'askQty': ce_data.get('askQty', 0)
                }
            
            if 'PE' in item:
                pe_data = item['PE']
                puts[strike] = {
                    'openInterest': pe_data.get('openInterest', 0),  # Use raw OI value from NSE
                    'changeinOpenInterest': pe_data.get('changeinOpenInterest', 0),
                    'totalTradedVolume': pe_data.get('totalTradedVolume', 0),
                    'volume': pe_data.get('totalTradedVolume', 0),  # Map for frontend compatibility
                    'impliedVolatility': pe_data.get('impliedVolatility', 0),
                    'iv': pe_data.get('impliedVolatility', 0),  # Map for frontend compatibility
                    'lastPrice': pe_data.get('lastPrice', 0),
                    'change': pe_data.get('change', 0),
                    'bidQty': pe_data.get('bidQty', 0),
                    'bidprice': pe_data.get('bidprice', 0),
                    'askPrice': pe_data.get('askPrice', 0),
                    'askQty': pe_data.get('askQty', 0)
                }
        
        return {
            "symbol": symbol,
            "spotPrice": spot_price,
            "expiryDate": expiry,
            "timestamp": timestamp,
            "strikePrices": strike_prices,
            "calls": calls,
            "puts": puts,
            "isLiveData": True,
            "message": None
        }
    except Exception as err:
        print(f"Error fetching options chain: {err}")
        # Return some default options chain data in case of error
        sample_data = generate_sample_options_chain(symbol, expiry)
        sample_data["isLiveData"] = False
        sample_data["message"] = f"Error fetching options chain: {str(err)}. Using sample data."
        return sample_data

def get_live_spot_price(symbol):
    """Get live spot price for an index or equity with caching"""
    current_time = time.time()
    
    # Check if we have a cached value that's still valid
    if symbol in spot_price_cache and symbol in spot_price_cache_time:
        if current_time - spot_price_cache_time[symbol] < CACHE_EXPIRY_SECONDS:
            return spot_price_cache[symbol], True  # True indicates live data (from cache)
    
    try:
        # Ensure we have valid cookies
        if not cookies or len(cookies) == 0:
            refresh_session()
        
        # Use appropriate URL based on symbol type
        url = (url_index if is_index(symbol) else url_stock) + symbol
        
        # Make the request with a delay
        response = session.get(url, headers=headers, timeout=10, cookies=cookies)
        
        # Check if we got a valid response
        if response.status_code != 200:
            print(f"Error fetching spot price: Status code {response.status_code}")
            if response.status_code == 401 or response.status_code == 403:
                refresh_session()
                response = session.get(url, headers=headers, timeout=10, cookies=cookies)
        
        # Try to parse the JSON
        try:
            json_data = response.json()
            records = json_data.get('records', {})
            
            # Get spot price differently for indices vs equities
            if is_index(symbol):
                spot_price = records.get('underlyingValue', 0)
            else:
                # For equities, try getting the underlying value first, then stocPrice if that's not available
                spot_price = records.get('underlyingValue', records.get('stocPrice', records.get('spotPrice', 0)))
            
            if not spot_price:
                print(f"Warning: Got zero spot price for {symbol}")
                return 0, False
            
            # Update cache
            spot_price_cache[symbol] = spot_price
            spot_price_cache_time[symbol] = current_time
            
            return spot_price, True  # True indicates live data
        except Exception as err:
            print(f"Error parsing JSON for spot price: {err}")
            print(f"Response content: {response.text[:200]}...")  # Print first 200 chars for debugging
            return 0, False  # Return 0 to indicate error
            
    except Exception as err:
        print(f"Error fetching live spot price: {err}")
        return 0, False  # Return 0 to indicate error

def generate_default_expiry_dates(symbol):
    """Generate default expiry dates based on the index"""
    today = datetime.datetime.now()
    
    # Weekly expiries for NIFTY, BANKNIFTY, FINNIFTY
    if symbol in ["NIFTY", "BANKNIFTY", "FINNIFTY"]:
        # Find the next Thursday
        days_until_thursday = (3 - today.weekday()) % 7
        next_thursday = today + datetime.timedelta(days=days_until_thursday)
        
        # Generate 4 weekly expiries (Thursdays)
        return [
            (next_thursday + datetime.timedelta(days=i*7)).strftime("%d-%b-%Y") 
            for i in range(4)
        ]
    # Monthly expiries for other indices
    else:
        # Find the last Thursday of current month
        last_day = datetime.datetime(today.year, today.month + 1, 1) - datetime.timedelta(days=1)
        offset = (3 - last_day.weekday()) % 7
        last_thursday = last_day - datetime.timedelta(days=offset)
        
        # Generate 3 monthly expiries
        expiries = []
        current = last_thursday
        for _ in range(3):
            expiries.append(current.strftime("%d-%b-%Y"))
            # Move to next month
            if current.month == 12:
                next_month = datetime.datetime(current.year + 1, 1, 1)
            else:
                next_month = datetime.datetime(current.year, current.month + 1, 1)
            last_day = datetime.datetime(next_month.year, next_month.month + 1, 1) - datetime.timedelta(days=1)
            offset = (3 - last_day.weekday()) % 7
            current = last_day - datetime.timedelta(days=offset)
            
        return expiries

def generate_sample_options_chain(symbol, expiry):
    """Generate sample options chain data for development/fallback"""
    
    # Try to get live spot price first
    spot_price, is_live_spot = get_live_spot_price(symbol)
    
    # Generate strike prices around the spot price with correct intervals
    if symbol == "BANKNIFTY":
        # BANKNIFTY uses 100-point intervals
        base = round(spot_price / 100) * 100
        strike_prices = [base + (i - 10) * 100 for i in range(21)]
    elif symbol == "NIFTY":
        # NIFTY uses 50-point intervals
        base = round(spot_price / 50) * 50
        strike_prices = [base + (i - 10) * 50 for i in range(21)]
    elif symbol == "FINNIFTY":
        # FINNIFTY uses 50-point intervals
        base = round(spot_price / 50) * 50
        strike_prices = [base + (i - 10) * 50 for i in range(21)]
    else:
        # Default 50-point intervals for other indices
        base = round(spot_price / 50) * 50
        strike_prices = [base + (i - 10) * 50 for i in range(21)]
    
    # Generate call and put data
    calls = {}
    puts = {}
    
    for strike in strike_prices:
        # Calculate distance from spot as percentage for realistic values
        distance_pct = abs((strike - spot_price) / spot_price)
        
        # Use more realistic OI values (typically in tens of thousands, not millions)
        # Call option data
        call_price = max(0.1, spot_price - strike + random.uniform(-200, 200))
        volume_val = int(random.uniform(1000, 50000) * (1 - distance_pct))
        iv_val = max(5, 20 + (distance_pct * 100) + random.uniform(-5, 5))
        calls[strike] = {
            'openInterest': int(random.uniform(10000, 100000) * (1 - distance_pct)),  # More realistic OI
            'changeinOpenInterest': int(random.uniform(-5000, 5000)),
            'totalTradedVolume': volume_val,
            'volume': volume_val,  # Map for frontend compatibility
            'impliedVolatility': iv_val,
            'iv': iv_val,  # Map for frontend compatibility
            'lastPrice': max(0.1, call_price),
            'change': random.uniform(-10, 10),
            'bidQty': int(random.uniform(100, 1000)),
            'bidprice': max(0.1, call_price - random.uniform(1, 5)),
            'askPrice': max(0.1, call_price + random.uniform(1, 5)),
            'askQty': int(random.uniform(100, 1000))
        }
        
        # Put option data
        put_price = max(0.1, strike - spot_price + random.uniform(-200, 200))
        volume_val = int(random.uniform(1000, 50000) * (1 - distance_pct))
        iv_val = max(5, 20 + (distance_pct * 100) + random.uniform(-5, 5))
        puts[strike] = {
            'openInterest': int(random.uniform(10000, 100000) * (1 - distance_pct)),  # More realistic OI
            'changeinOpenInterest': int(random.uniform(-5000, 5000)),
            'totalTradedVolume': volume_val,
            'volume': volume_val,  # Map for frontend compatibility
            'impliedVolatility': iv_val,
            'iv': iv_val,  # Map for frontend compatibility
            'lastPrice': max(0.1, put_price),
            'change': random.uniform(-10, 10),
            'bidQty': int(random.uniform(100, 1000)),
            'bidprice': max(0.1, put_price - random.uniform(1, 5)),
            'askPrice': max(0.1, put_price + random.uniform(1, 5)),
            'askQty': int(random.uniform(100, 1000))
        }
    
    return {
        "symbol": symbol,
        "spotPrice": spot_price,
        "expiryDate": expiry,
        "timestamp": datetime.datetime.now().strftime("%d-%b-%Y %H:%M:%S"),
        "strikePrices": strike_prices,
        "calls": calls,
        "puts": puts,
        "spotPriceIsLive": is_live_spot
    }

@app.get("/api/raw-options-data")
async def get_raw_options_data(symbol: str, expiry: str, strike: float):
    """Get raw options data for a specific strike price for debugging"""
    try:
        # Ensure we have valid cookies
        if not cookies or len(cookies) == 0:
            if not refresh_session():
                return {"error": "Failed to refresh session"}
        
        url = url_index + symbol
        
        # Make the request with a delay
        response = session.get(url, headers=headers, timeout=10, cookies=cookies)
        
        # Check if we got a valid response
        if response.status_code != 200:
            print(f"Error fetching options data: Status code {response.status_code}")
            if response.status_code == 401 or response.status_code == 403:
                if refresh_session():
                    response = session.get(url, headers=headers, timeout=10, cookies=cookies)
                else:
                    return {"error": "Failed to refresh session"}
        
        # Try to parse the JSON
        try:
            json_data = response.json()
        except Exception as err:
            # Try manual decompression if needed
            try:
                import json
                content_encoding = response.headers.get('content-encoding', '').lower()
                if content_encoding == 'gzip':
                    import gzip
                    decompressed = gzip.decompress(response.content)
                    text_content = decompressed.decode('utf-8')
                elif content_encoding == 'br':
                    try:
                        import brotli
                        decompressed = brotli.decompress(response.content)
                        text_content = decompressed.decode('utf-8')
                    except ImportError:
                        print("Brotli library not available for raw data endpoint")
                        text_content = response.text
                else:
                    text_content = response.text
                json_data = json.loads(text_content)
                print("Raw data: Successfully parsed JSON using manual decompression!")
            except Exception as err2:
                print(f"Error parsing JSON (both methods): {err}, {err2}")
                return {"error": f"Failed to parse JSON: {str(err)}"}
        
        # Process the data
        records = json_data.get('records', {})
        filtered_data = [item for item in records.get('data', []) 
                         if item.get('expiryDate') == expiry and item.get('strikePrice') == strike]
        
        if not filtered_data:
            return {"error": f"No data found for {symbol} with expiry {expiry} and strike {strike}"}
        
        # Return the raw data for inspection
        return {
            "rawData": filtered_data,
            "timestamp": records.get('timestamp', '')
        }
    except Exception as err:
        print(f"Error fetching raw options data: {err}")
        return {"error": f"Error fetching raw options data: {str(err)}"}

@app.get("/api/compare-oi")
async def compare_oi(symbol: str, expiry: str, strike: float):
    """Compare OI values from our API with raw NSE data"""
    try:
        # Get data from our options chain endpoint
        options_chain = await get_options_chain(symbol, expiry)

        # Get raw data directly from NSE
        raw_data = await get_raw_options_data(symbol, expiry, strike)

        # Extract OI values from both sources
        our_call_oi = options_chain.get("calls", {}).get(strike, {}).get("openInterest", "N/A")
        our_put_oi = options_chain.get("puts", {}).get(strike, {}).get("openInterest", "N/A")

        raw_call_oi = "N/A"
        raw_put_oi = "N/A"

        if "rawData" in raw_data and raw_data["rawData"]:
            for item in raw_data["rawData"]:
                if "CE" in item:
                    raw_call_oi = item["CE"].get("openInterest", "N/A")
                if "PE" in item:
                    raw_put_oi = item["PE"].get("openInterest", "N/A")

        return {
            "symbol": symbol,
            "expiry": expiry,
            "strike": strike,
            "ourData": {
                "callOI": our_call_oi,
                "putOI": our_put_oi,
                "isLiveData": options_chain.get("isLiveData", False),
                "message": options_chain.get("message", "")
            },
            "rawNseData": {
                "callOI": raw_call_oi,
                "putOI": raw_put_oi
            },
            "timestamp": raw_data.get("timestamp", ""),
            "dataMatch": our_call_oi == raw_call_oi and our_put_oi == raw_put_oi,
            "debugInfo": {
                "cookiesAvailable": len(cookies) > 0,
                "sessionActive": session is not None,
                "lastRequestTime": last_request_time
            }
        }
    except Exception as err:
        print(f"Error comparing OI values: {err}")
        return {"error": f"Error comparing OI values: {str(err)}"}

@app.get("/api/test-nse-connection")
async def test_nse_connection():
    """Test NSE connectivity and session status"""
    try:
        # Test basic connectivity
        test_url = "https://www.nseindia.com/"
        response = session.get(test_url, timeout=10)

        # Test API endpoint
        api_url = url_index + "NIFTY"
        api_response = session.get(api_url, headers=headers, timeout=10, cookies=cookies)

        # Try to parse the API response
        api_json_parseable = False
        api_content_preview = api_response.text[:100]

        try:
            api_response.json()
            api_json_parseable = True
        except:
            # Try manual decompression
            try:
                import json
                content_encoding = api_response.headers.get('content-encoding', '').lower()
                if content_encoding == 'gzip':
                    import gzip
                    decompressed = gzip.decompress(api_response.content)
                    text_content = decompressed.decode('utf-8')
                    api_content_preview = text_content[:100]
                elif content_encoding == 'br':
                    try:
                        import brotli
                        decompressed = brotli.decompress(api_response.content)
                        text_content = decompressed.decode('utf-8')
                        api_content_preview = text_content[:100]
                    except ImportError:
                        text_content = api_response.text
                else:
                    text_content = api_response.text
                json.loads(text_content)
                api_json_parseable = True
            except:
                pass

        return {
            "nseHomepage": {
                "status": response.status_code,
                "accessible": response.status_code == 200,
                "contentType": response.headers.get('content-type', ''),
                "contentLength": len(response.text)
            },
            "nseApiEndpoint": {
                "status": api_response.status_code,
                "accessible": api_response.status_code == 200,
                "contentType": api_response.headers.get('content-type', ''),
                "contentEncoding": api_response.headers.get('content-encoding', ''),
                "contentLength": len(api_response.text),
                "isJson": api_response.text.strip().startswith('{'),
                "isHtml": api_response.text.strip().startswith('<!DOCTYPE') or api_response.text.strip().startswith('<html'),
                "jsonParseable": api_json_parseable,
                "firstChars": api_content_preview
            },
            "sessionInfo": {
                "cookiesCount": len(cookies),
                "cookieNames": list(cookies.keys()),
                "sessionActive": session is not None,
                "lastRequestTime": last_request_time
            }
        }
    except Exception as err:
        return {"error": f"Connection test failed: {str(err)}"}

@app.get("/api/debug-strike")
async def debug_strike_data(symbol: str = "NIFTY", expiry: str = "", strike: float = 24700):
    """Debug endpoint to check specific strike price data"""
    try:
        # Get the current expiry if not provided
        if not expiry:
            expiry_dates = await get_expiry_dates(symbol)
            expiry = expiry_dates[0] if expiry_dates else ""

        # Get options chain data
        options_data = await get_options_chain(symbol, expiry)

        # Get raw NSE data for comparison
        raw_data = await get_raw_options_data(symbol, expiry, strike)

        # Extract specific strike data
        call_data = options_data.get("calls", {}).get(strike, {})
        put_data = options_data.get("puts", {}).get(strike, {})

        return {
            "symbol": symbol,
            "expiry": expiry,
            "strike": strike,
            "spotPrice": options_data.get("spotPrice", 0),
            "callData": call_data,
            "putData": put_data,
            "isLiveData": options_data.get("isLiveData", False),
            "message": options_data.get("message", ""),
            "rawNseResponse": raw_data,
            "timestamp": options_data.get("timestamp", ""),
            "debugInfo": {
                "totalStrikes": len(options_data.get("strikePrices", [])),
                "strikeExists": strike in options_data.get("calls", {}),
                "cookiesCount": len(cookies),
                "sessionStatus": "active" if session else "inactive"
            }
        }
    except Exception as err:
        return {"error": f"Debug error: {str(err)}"}

def generate_default_stock_expiry_dates(symbol):
    """Generate default expiry dates for stocks (monthly expiries)"""
    today = datetime.datetime.now()

    # Find the last Thursday of current month
    last_day = datetime.datetime(today.year, today.month + 1, 1) - datetime.timedelta(days=1)
    offset = (3 - last_day.weekday()) % 7
    last_thursday = last_day - datetime.timedelta(days=offset)

    # Generate 3 monthly expiries
    expiries = []
    current = last_thursday
    for _ in range(3):
        expiries.append(current.strftime("%d-%b-%Y"))
        # Move to next month
        if current.month == 12:
            next_month = datetime.datetime(current.year + 1, 1, 1)
        else:
            next_month = datetime.datetime(current.year, current.month + 1, 1)
        last_day = datetime.datetime(next_month.year, next_month.month + 1, 1) - datetime.timedelta(days=1)
        offset = (3 - last_day.weekday()) % 7
        current = last_day - datetime.timedelta(days=offset)

    return expiries

def generate_sample_stock_options_chain(symbol, expiry):
    """Generate sample stock options chain data for development/fallback"""

    # Stock fallback prices
    stock_fallback_prices = {
        "INFY": 721.00,
        "TCS": 3250.00,
        "RELIANCE": 2450.00,
        "HDFCBANK": 1650.00,
        "ICICIBANK": 1150.00,
        "SBIN": 820.00,
        "BHARTIARTL": 1520.00,
        "ITC": 465.00,
        "KOTAKBANK": 1750.00,
        "LT": 3650.00
    }

    spot_price = stock_fallback_prices.get(symbol, 1000.00)

    # Generate strike prices around the spot price with appropriate intervals
    # Most stocks use 2.5 or 5 point intervals depending on price
    if spot_price < 500:
        interval = 2.5
    elif spot_price < 1000:
        interval = 5
    elif spot_price < 2000:
        interval = 10
    else:
        interval = 25

    base = round(spot_price / interval) * interval
    strike_prices = [base + (i - 10) * interval for i in range(21)]

    # Generate call and put data
    calls = {}
    puts = {}

    for strike in strike_prices:
        # Calculate distance from spot as percentage for realistic values
        distance_pct = abs((strike - spot_price) / spot_price)

        # Call option data
        call_price = max(0.05, spot_price - strike + random.uniform(-50, 50))
        volume_val = int(random.uniform(100, 5000) * (1 - distance_pct))
        iv_val = max(5, 25 + (distance_pct * 50) + random.uniform(-5, 5))
        calls[strike] = {
            'openInterest': int(random.uniform(1000, 20000) * (1 - distance_pct)),
            'changeinOpenInterest': int(random.uniform(-1000, 1000)),
            'totalTradedVolume': volume_val,
            'volume': volume_val,
            'impliedVolatility': iv_val,
            'iv': iv_val,
            'lastPrice': max(0.05, call_price),
            'change': random.uniform(-5, 5),
            'bidQty': int(random.uniform(50, 500)),
            'bidprice': max(0.05, call_price - random.uniform(0.5, 2)),
            'askPrice': max(0.05, call_price + random.uniform(0.5, 2)),
            'askQty': int(random.uniform(50, 500))
        }

        # Put option data
        put_price = max(0.05, strike - spot_price + random.uniform(-50, 50))
        volume_val = int(random.uniform(100, 5000) * (1 - distance_pct))
        iv_val = max(5, 25 + (distance_pct * 50) + random.uniform(-5, 5))
        puts[strike] = {
            'openInterest': int(random.uniform(1000, 20000) * (1 - distance_pct)),
            'changeinOpenInterest': int(random.uniform(-1000, 1000)),
            'totalTradedVolume': volume_val,
            'volume': volume_val,
            'impliedVolatility': iv_val,
            'iv': iv_val,
            'lastPrice': max(0.05, put_price),
            'change': random.uniform(-5, 5),
            'bidQty': int(random.uniform(50, 500)),
            'bidprice': max(0.05, put_price - random.uniform(0.5, 2)),
            'askPrice': max(0.05, put_price + random.uniform(0.5, 2)),
            'askQty': int(random.uniform(50, 500))
        }

    return {
        "symbol": symbol,
        "spotPrice": spot_price,
        "expiryDate": expiry,
        "timestamp": datetime.datetime.now().strftime("%d-%b-%Y %H:%M:%S"),
        "strikePrices": strike_prices,
        "calls": calls,
        "puts": puts,
        "spotPriceIsLive": False
    }

@app.get("/api/stock-options-chain")
async def get_stock_options_chain(symbol: str, expiry: str):
    """Get options chain data for a stock and expiry date"""
    try:
        # Ensure we have valid cookies
        if not cookies or len(cookies) == 0:
            if not refresh_session():
                # Return some default stock options chain data
                sample_data = generate_sample_stock_options_chain(symbol, expiry)
                sample_data = generate_sample_stock_options_chain(symbol, expiry)
                sample_data["isLiveData"] = False
                sample_data["message"] = "Using sample data. Unable to fetch live data from NSE."
                return sample_data

        url = url_stock + symbol

        # Make the request with a delay
        response = session.get(url, headers=headers, timeout=10, cookies=cookies)

        # Check if we got a valid response
        if response.status_code != 200:
            print(f"Error fetching stock options chain: Status code {response.status_code}")
            if response.status_code == 401 or response.status_code == 403:
                if refresh_session():
                    response = session.get(url, headers=headers, timeout=10, cookies=cookies)
                else:
                    sample_data = generate_sample_stock_options_chain(symbol, expiry)
                    sample_data["isLiveData"] = False
                    sample_data["message"] = "Using sample data. Unable to fetch live data from NSE."
                    return sample_data

        # Try to parse the JSON - let requests handle decompression
        try:
            json_data = response.json()
            print(f"Successfully parsed stock options JSON for {symbol}")
        except Exception as err:
            print(f"Error parsing stock options JSON: {err}")
            print(f"Response status: {response.status_code}")
            print(f"Response headers: {dict(response.headers)}")
            print(f"Response content preview: {response.text[:500]}...")

            # Check if response is HTML (NSE blocking us)
            if response.text.strip().startswith('<!DOCTYPE') or response.text.strip().startswith('<html'):
                print("NSE returned HTML instead of JSON - likely blocked or redirected")
                # Try to refresh session and retry once
                if refresh_session():
                    print("Retrying stock options after session refresh...")
                    time.sleep(2)
                    retry_response = session.get(url, headers=headers, timeout=10, cookies=cookies)
                    try:
                        json_data = retry_response.json()
                        print("Stock options retry successful!")
                    except:
                        print("Stock options retry also failed, using sample data")
                        sample_data = generate_sample_stock_options_chain(symbol, expiry)
                        sample_data["isLiveData"] = False
                        sample_data["message"] = "Using sample data. NSE blocked requests (returned HTML)."
                        return sample_data
                else:
                    sample_data = generate_sample_stock_options_chain(symbol, expiry)
                    sample_data["isLiveData"] = False
                    sample_data["message"] = "Using sample data. Unable to refresh NSE session."
                    return sample_data
            else:
                # Return sample data for other parsing errors
                sample_data = generate_sample_stock_options_chain(symbol, expiry)
                sample_data["isLiveData"] = False
                sample_data["message"] = f"Using sample data. Unable to parse JSON response from NSE. Error: {str(err)}"
                return sample_data

        # Process the data
        records = json_data.get('records', {})
        filtered_data = [item for item in records.get('data', []) if item.get('expiryDate') == expiry]

        # If no data for this expiry, return sample data
        if not filtered_data:
            print(f"No stock data found for {symbol} with expiry {expiry}")
            sample_data = generate_sample_stock_options_chain(symbol, expiry)
            sample_data["isLiveData"] = False
            sample_data["message"] = f"No data found for {symbol} with expiry {expiry}. Using sample data."
            return sample_data

        # Get the exact spot price without rounding
        spot_price = records.get('underlyingValue', 0)
        timestamp = records.get('timestamp', '')

        # Extract strike prices - these are already properly rounded by NSE
        strike_prices = sorted(list(set(item.get('strikePrice', 0) for item in filtered_data)))

        # Process call and put data
        calls = {}
        puts = {}

        for item in filtered_data:
            strike = item.get('strikePrice', 0)

            if 'CE' in item:
                ce_data = item['CE']
                calls[strike] = {
                    'openInterest': ce_data.get('openInterest', 0),
                    'changeinOpenInterest': ce_data.get('changeinOpenInterest', 0),
                    'totalTradedVolume': ce_data.get('totalTradedVolume', 0),
                    'volume': ce_data.get('totalTradedVolume', 0),
                    'impliedVolatility': ce_data.get('impliedVolatility', 0),
                    'iv': ce_data.get('impliedVolatility', 0),
                    'lastPrice': ce_data.get('lastPrice', 0),
                    'change': ce_data.get('change', 0),
                    'bidQty': ce_data.get('bidQty', 0),
                    'bidprice': ce_data.get('bidprice', 0),
                    'askPrice': ce_data.get('askPrice', 0),
                    'askQty': ce_data.get('askQty', 0)
                }

            if 'PE' in item:
                pe_data = item['PE']
                puts[strike] = {
                    'openInterest': pe_data.get('openInterest', 0),
                    'changeinOpenInterest': pe_data.get('changeinOpenInterest', 0),
                    'totalTradedVolume': pe_data.get('totalTradedVolume', 0),
                    'volume': pe_data.get('totalTradedVolume', 0),
                    'impliedVolatility': pe_data.get('impliedVolatility', 0),
                    'iv': pe_data.get('impliedVolatility', 0),
                    'lastPrice': pe_data.get('lastPrice', 0),
                    'change': pe_data.get('change', 0),
                    'bidQty': pe_data.get('bidQty', 0),
                    'bidprice': pe_data.get('bidprice', 0),
                    'askPrice': pe_data.get('askPrice', 0),
                    'askQty': pe_data.get('askQty', 0)
                }

        return {
            "symbol": symbol,
            "spotPrice": spot_price,
            "expiryDate": expiry,
            "timestamp": timestamp,
            "strikePrices": strike_prices,
            "calls": calls,
            "puts": puts,
            "isLiveData": True,
            "message": None
        }
    except Exception as err:
        print(f"Error fetching stock options chain: {err}")
        # Return some default stock options chain data in case of error
        sample_data = generate_sample_stock_options_chain(symbol, expiry)
        sample_data["isLiveData"] = False
        sample_data["message"] = f"Error fetching stock options chain: {str(err)}. Using sample data."
        return sample_data

@app.get("/api/stock-expiry-dates")
async def get_stock_expiry_dates(symbol: str):
    """Get available expiry dates for a stock"""
    try:
        # Ensure we have valid cookies
        if not cookies:
            if not refresh_session():
                # Return some default expiry dates based on the stock
                return generate_default_stock_expiry_dates(symbol)

        url = url_stock + symbol

        # Make the request with a delay
        response = session.get(url, headers=headers, timeout=10, cookies=cookies)

        # Check if we got a valid response
        if response.status_code != 200:
            print(f"Error fetching stock expiry dates: Status code {response.status_code}")
            if response.status_code == 401 or response.status_code == 403:
                refresh_session()
                response = session.get(url, headers=headers, timeout=10, cookies=cookies)

        # Try to parse the JSON - let requests handle decompression
        try:
            json_data = response.json()
            print(f"Successfully parsed stock expiry dates JSON for {symbol}")
        except Exception as err:
            print(f"Error parsing stock expiry JSON: {err}")
            print(f"Response content: {response.text[:200]}...")
            return generate_default_stock_expiry_dates(symbol)

        expiry_dates = json_data.get('records', {}).get('expiryDates', [])

        # If no expiry dates found, return defaults
        if not expiry_dates:
            return generate_default_stock_expiry_dates(symbol)

        return expiry_dates
    except Exception as err:
        print(f"Error fetching stock expiry dates: {err}")
        return generate_default_stock_expiry_dates(symbol)

def is_index(symbol):
    """Determine if a symbol is an index or equity"""
    return symbol in ["NIFTY", "BANKNIFTY", "FINNIFTY", "MIDCPNIFTY"]

if __name__ == "__main__":
    import uvicorn
    print("Starting NSE API Service...")
    print("Available endpoints:")
    print("  GET /api/indices - Get available indices")
    print("  GET /api/expiry-dates?symbol=NIFTY - Get expiry dates for an index")
    print("  GET /api/market-data?symbol=NIFTY - Get current market data")
    print("  GET /api/options-chain?symbol=NIFTY&expiry=28-Nov-2024 - Get options chain")
    print("  GET /api/stock-options-chain?symbol=INFY&expiry=28-Nov-2024 - Get stock options chain")
    print("  GET /api/stock-expiry-dates?symbol=INFY - Get stock expiry dates")
    print("  GET /api/raw-options-data?symbol=NIFTY&expiry=28-Nov-2024&strike=24750 - Get raw options data")
    print("  GET /health - Health check")
    print("  GET / - API documentation")
    print("\nStarting server on http://localhost:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)



























